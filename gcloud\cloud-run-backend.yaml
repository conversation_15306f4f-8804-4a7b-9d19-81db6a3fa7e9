apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: digital-contract-backend
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "1000m"
        run.googleapis.com/max-scale: "10"
        run.googleapis.com/min-scale: "1"
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      containers:
      - image: gcr.io/PROJECT_ID/digital-contract-backend:latest
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3001"
        - name: MONGO_URI
          valueFrom:
            secretKeyRef:
              name: mongo-uri
              key: uri
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-url
              key: url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: SESSION_SECRET
          valueFrom:
            secretKeyRef:
              name: session-secret
              key: secret
        - name: SOLANA_CLUSTER
          value: "mainnet-beta"
        - name: PLATFORM_FEE_RECIPIENT_PRIVATE_KEY
          valueFrom:
            secretKeyRef:
              name: platform-fee-key
              key: private-key
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: GOOGLE_CLOUD_STORAGE_BUCKET
          value: "PROJECT_ID-contract-storage"
        resources:
          limits:
            cpu: "1000m"
            memory: "2Gi"
          requests:
            cpu: "500m"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
