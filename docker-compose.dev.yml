version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: digital-contract-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: digital_contracts
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - digital-contract-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: digital-contract-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - digital-contract-network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # IPFS Node (for file storage)
  ipfs:
    image: ipfs/kubo:latest
    container_name: digital-contract-ipfs
    restart: unless-stopped
    environment:
      - IPFS_PROFILE=server
    ports:
      - "4001:4001"     # P2P port
      - "5001:5001"     # API port
      - "8080:8080"     # Gateway port (changed to avoid conflict)
    volumes:
      - ipfs_data:/data/ipfs
    networks:
      - digital-contract-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5001/api/v0/version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MongoDB Express (Database Admin UI)
  mongo-express:
    image: mongo-express:1.0.2
    container_name: digital-contract-mongo-express
    restart: unless-stopped
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    ports:
      - "8081:8081"
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - digital-contract-network

  # Redis Commander (Redis Admin UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: digital-contract-redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8082:8081"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - digital-contract-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  ipfs_data:
    driver: local

networks:
  digital-contract-network:
    driver: bridge
