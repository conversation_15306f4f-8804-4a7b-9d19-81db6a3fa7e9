{"rustc_fingerprint": 17266092705596985283, "outputs": {"4614504638168534921": {"success": true, "status": "", "code": 0, "stdout": "rustc 1.75.0-dev\nbinary: rustc\ncommit-hash: unknown\ncommit-date: unknown\nhost: x86_64-pc-windows-msvc\nrelease: 1.75.0-dev\nLLVM version: 17.0.6\n", "stderr": ""}, "15729799797837862367": {"success": true, "status": "", "code": 0, "stdout": "___.exe\nlib___.rlib\n___.dll\n___.dll\n___.lib\n___.dll\nC:\\Users\\<USER>\\.cache\\solana\\v1.41\\platform-tools\\rust\npacked\n___\ndebug_assertions\noverflow_checks\npanic=\"unwind\"\nproc_macro\nrelocation_model=\"pic\"\ntarget_abi=\"\"\ntarget_arch=\"x86_64\"\ntarget_endian=\"little\"\ntarget_env=\"msvc\"\ntarget_family=\"windows\"\ntarget_feature=\"fxsr\"\ntarget_feature=\"sse\"\ntarget_feature=\"sse2\"\ntarget_has_atomic\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"64\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"64\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_os=\"windows\"\ntarget_pointer_width=\"64\"\ntarget_thread_local\ntarget_vendor=\"pc\"\nwindows\n", "stderr": ""}, "15190048980512435355": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\n___.so\nlib___.a\nC:\\Users\\<USER>\\.cache\\solana\\v1.41\\platform-tools\\rust\noff\n___\ndebug_assertions\noverflow_checks\npanic=\"abort\"\nproc_macro\nrelocation_model=\"pic\"\ntarget_abi=\"\"\ntarget_arch=\"sbf\"\ntarget_endian=\"little\"\ntarget_env=\"\"\ntarget_family=\"solana\"\ntarget_has_atomic\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"64\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"64\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_os=\"solana\"\ntarget_pointer_width=\"64\"\ntarget_vendor=\"solana\"\n", "stderr": "warning: dropping unsupported crate type `dylib` for target `sbf-solana-solana`\n\nwarning: dropping unsupported crate type `proc-macro` for target `sbf-solana-solana`\n\nwarning: 2 warnings emitted\n\n"}}, "successes": {}}