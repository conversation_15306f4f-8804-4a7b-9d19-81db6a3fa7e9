{"rustc": 18260301964069568163, "features": "[]", "declared_features": "", "target": 2217066858495368777, "profile": 10243973527296709326, "path": 13456570980622598903, "deps": [[1518464631554429769, "solana_hash", false, 10612626136558187406], [3439881079477119364, "serde_derive", false, 5590249711903712716], [5356478757179768432, "solana_instruction", false, 1640735690009512405], [9917880067899433992, "solana_sdk_ids", false, 10140495928457920276], [10633404241517405153, "serde", false, 9035659464674566963], [12057582310199615885, "solana_message", false, 11156837848261918566], [12809794537881357198, "thiserror", false, 14027666962946330320], [14148824040489233401, "solana_clock", false, 8166372440451125004], [14901974103992976078, "solana_pubkey", false, 15439063490133519485], [15585056855647917333, "solana_keccak_hasher", false, 8852290972604894867], [16694830413560736608, "solana_address_lookup_table_interface", false, 12566008450371767828], [17747848508026429195, "solana_system_interface", false, 3097912800306758407], [18106254882260006778, "solana_nonce", false, 13496046238173061540]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\solana-example-mocks-5fd1c9ee49345622\\dep-lib-solana_example_mocks"}}], "rustflags": [], "metadata": 7454496256326730147, "config": 2202906307356721367, "compile_kind": 0}