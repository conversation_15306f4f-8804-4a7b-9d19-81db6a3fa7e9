name: Deploy to Google Cloud Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
    types: [closed]

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: us-central1
  BACKEND_SERVICE: digital-contract-backend
  FRONTEND_SERVICE: digital-contract-frontend

jobs:
  test:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install frontend dependencies
      run: npm ci

    - name: Install backend dependencies
      run: |
        cd backend
        npm ci

    - name: Run frontend tests
      run: npm test -- --watchAll=false --coverage

    - name: Run backend tests
      run: |
        cd backend
        npm test

    - name: Run linting
      run: |
        npm run lint
        cd backend
        npm run lint

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}
        export_default_credentials: true

    - name: Configure Docker for GCR
      run: gcloud auth configure-docker

    - name: Build backend image
      run: |
        docker build -t gcr.io/$PROJECT_ID/$BACKEND_SERVICE:$GITHUB_SHA \
                     -t gcr.io/$PROJECT_ID/$BACKEND_SERVICE:latest \
                     ./backend

    - name: Build frontend image
      run: |
        docker build -f Dockerfile.production \
                     -t gcr.io/$PROJECT_ID/$FRONTEND_SERVICE:$GITHUB_SHA \
                     -t gcr.io/$PROJECT_ID/$FRONTEND_SERVICE:latest \
                     .

    - name: Push images to GCR
      run: |
        docker push gcr.io/$PROJECT_ID/$BACKEND_SERVICE:$GITHUB_SHA
        docker push gcr.io/$PROJECT_ID/$BACKEND_SERVICE:latest
        docker push gcr.io/$PROJECT_ID/$FRONTEND_SERVICE:$GITHUB_SHA
        docker push gcr.io/$PROJECT_ID/$FRONTEND_SERVICE:latest

    - name: Deploy backend to Cloud Run
      run: |
        gcloud run deploy $BACKEND_SERVICE \
          --image gcr.io/$PROJECT_ID/$BACKEND_SERVICE:$GITHUB_SHA \
          --region $REGION \
          --platform managed \
          --allow-unauthenticated \
          --memory 2Gi \
          --cpu 1 \
          --max-instances 10 \
          --min-instances 1 \
          --port 3001 \
          --set-env-vars NODE_ENV=production \
          --set-env-vars SOLANA_CLUSTER=mainnet-beta \
          --set-secrets MONGO_URI=mongo-uri:latest \
          --set-secrets REDIS_URL=redis-url:latest \
          --set-secrets JWT_SECRET=jwt-secret:latest \
          --set-secrets SESSION_SECRET=session-secret:latest \
          --set-secrets PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=platform-fee-key:latest

    - name: Deploy frontend to Cloud Run
      run: |
        gcloud run deploy $FRONTEND_SERVICE \
          --image gcr.io/$PROJECT_ID/$FRONTEND_SERVICE:$GITHUB_SHA \
          --region $REGION \
          --platform managed \
          --allow-unauthenticated \
          --memory 1Gi \
          --cpu 0.5 \
          --max-instances 20 \
          --min-instances 1 \
          --port 8080 \
          --set-env-vars NODE_ENV=production \
          --set-env-vars REACT_APP_SOLANA_CLUSTER=mainnet-beta

    - name: Get service URLs
      id: get-urls
      run: |
        BACKEND_URL=$(gcloud run services describe $BACKEND_SERVICE --region=$REGION --format="value(status.url)")
        FRONTEND_URL=$(gcloud run services describe $FRONTEND_SERVICE --region=$REGION --format="value(status.url)")
        echo "backend-url=$BACKEND_URL" >> $GITHUB_OUTPUT
        echo "frontend-url=$FRONTEND_URL" >> $GITHUB_OUTPUT

    - name: Update frontend with backend URL
      run: |
        gcloud run services update $FRONTEND_SERVICE \
          --region $REGION \
          --set-env-vars REACT_APP_API_URL=${{ steps.get-urls.outputs.backend-url }}

    - name: Run health checks
      run: |
        echo "Waiting for services to be ready..."
        sleep 30
        
        # Check backend health
        curl -f ${{ steps.get-urls.outputs.backend-url }}/api/health || exit 1
        
        # Check frontend
        curl -f ${{ steps.get-urls.outputs.frontend-url }} || exit 1

    - name: Create deployment summary
      run: |
        echo "## 🚀 Deployment Successful" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Service URLs:" >> $GITHUB_STEP_SUMMARY
        echo "- **Frontend**: ${{ steps.get-urls.outputs.frontend-url }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Backend**: ${{ steps.get-urls.outputs.backend-url }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Deployment Details:" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit**: $GITHUB_SHA" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch**: $GITHUB_REF_NAME" >> $GITHUB_STEP_SUMMARY
        echo "- **Region**: $REGION" >> $GITHUB_STEP_SUMMARY
        echo "- **Timestamp**: $(date -u)" >> $GITHUB_STEP_SUMMARY

  staging-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' && github.event.pull_request.merged == true
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}
        export_default_credentials: true

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add staging deployment logic here
        echo "Staging deployment completed"
