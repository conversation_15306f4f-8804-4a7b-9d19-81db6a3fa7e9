[toolchain]
anchor_version = "0.31.1"

[features]
seeds = false
skip-lint = false

[programs.devnet]
digital_contract = "7adrB6LCgkC8QSTPdrgzeBijzFA2aHzjKGrL9qk6xuxF"

[programs.localnet]
digital_contract = "7adrB6LCgkC8QSTPdrgzeBijzFA2aHzjKGrL9qk6xuxF"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "devnet"
wallet = "../../backend/wallet.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
