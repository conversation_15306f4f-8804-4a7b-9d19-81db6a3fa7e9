# Staging Environment Configuration
# Digital Contract Platform - Google Cloud Staging

# Application Environment
NODE_ENV=staging
PORT=3001

# Solana Configuration
SOLANA_CLUSTER=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com

# Platform Configuration
PLATFORM_FEE_AMOUNT=0.01
PLATFORM_FEE_RECIPIENT=BcSXav3jcBKRbN6woZsqPMJGYrS2MXwVEdtUx1ZzD9Xo

# Frontend Configuration (for build time)
REACT_APP_SOLANA_CLUSTER=devnet
REACT_APP_PLATFORM_FEE_RECIPIENT=BcSXav3jcBKRbN6woZsqPMJGYrS2MXwVEdtUx1ZzD9Xo
REACT_APP_PLATFORM_FEE_AMOUNT=0.01

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id-staging
GOOGLE_CLOUD_REGION=us-central1
GOOGLE_CLOUD_STORAGE_BUCKET=your-project-id-staging-contract-storage

# Security Configuration
CORS_ORIGIN=https://your-staging-frontend-domain.com
RATE_LIMIT_WINDOW_MS=300000
RATE_LIMIT_MAX_REQUESTS=200

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=pretty

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Note: Sensitive values like database URLs, API keys, and secrets
# should be stored in Google Cloud Secret Manager, not in this file
