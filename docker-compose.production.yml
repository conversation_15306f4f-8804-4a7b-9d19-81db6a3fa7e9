version: '3.8'

services:
  # Frontend (React + Nginx)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: digital_contract_frontend_prod
    restart: unless-stopped
    ports:
      - "80:8080"
      - "443:8080"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=http://backend:3001
      - REACT_APP_SOLANA_CLUSTER=mainnet-beta
      - REACT_APP_PLATFORM_FEE_RECIPIENT=BcSXav3jcBKRbN6woZsqPMJGYrS2MXwVEdtUx1ZzD9Xo
    networks:
      - digital_contract_network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend (Node.js API)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: digital_contract_backend_prod
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MONGO_URI=mongodb://admin:${MONGO_PASSWORD:-password123}@mongodb:27017/digital_contracts?authSource=admin
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret-here}
      - SESSION_SECRET=${SESSION_SECRET:-your-session-secret-here}
      - SOLANA_CLUSTER=mainnet-beta
      - PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=${PLATFORM_FEE_RECIPIENT_PRIVATE_KEY:-[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64]}
      - PLATFORM_FEE_AMOUNT=0.01
      - CORS_ORIGIN=http://localhost:80,http://localhost:8080
    networks:
      - digital_contract_network
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: securecontract_mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
      - MONGO_INITDB_DATABASE=digital_contracts
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - securecontract_network
    command: mongod --auth --bind_ip_all
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: securecontract_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - securecontract_network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # IPFS for document storage
  ipfs:
    image: ipfs/go-ipfs:latest
    container_name: securecontract_ipfs
    restart: unless-stopped
    ports:
      - "4001:4001"
      - "5001:5001"
      - "8081:8080"
    volumes:
      - ipfs_data:/data/ipfs
    networks:
      - securecontract_network
    environment:
      - IPFS_PROFILE=server
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5001/api/v0/version"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer (optional, for multiple frontend instances)
  nginx-lb:
    image: nginx:alpine
    container_name: securecontract_nginx_lb
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf:ro
    networks:
      - securecontract_network
    depends_on:
      - frontend
    profiles:
      - load-balancer

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: securecontract_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - securecontract_network
    profiles:
      - monitoring

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: securecontract_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - securecontract_network
    profiles:
      - monitoring

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local
  ipfs_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  digital_contract_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
